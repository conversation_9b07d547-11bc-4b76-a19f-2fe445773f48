{"folders": [{"name": "🏠 Root", "path": "."}, {"name": "📱 Frontend", "path": "./frontend"}, {"name": "⚡ Backend", "path": "./backend"}, {"name": "📚 Docs", "path": "./docs"}], "settings": {"typescript.preferences.includePackageJsonAutoImports": "auto", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "files.associations": {"*.vue": "vue", "*.obj.js": "javascript"}, "emmet.includeLanguages": {"vue": "html"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/unpackage": true, "**/.git": true}, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/unpackage": true}}, "extensions": {"recommendations": ["Vue.volar", "ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-vscode.vscode-json"]}, "tasks": {"version": "2.0.0", "tasks": [{"label": "🚀 启动前端开发服务器", "type": "shell", "command": "npm", "args": ["run", "dev:frontend"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "📦 构建前端", "type": "shell", "command": "npm", "args": ["run", "build:frontend"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "☁️ 部署后端", "type": "shell", "command": "npm", "args": ["run", "deploy:backend"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "🔄 安装所有依赖", "type": "shell", "command": "npm", "args": ["run", "install:all"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}]}, "launch": {"version": "0.2.0", "configurations": [{"name": "🐛 调试前端", "type": "node", "request": "launch", "program": "${workspaceFolder}/frontend/node_modules/@dcloudio/vite-plugin-uni/bin/uni.js", "args": ["dev"], "cwd": "${workspaceFolder}/frontend", "console": "integratedTerminal"}]}}