/**
 * 通用工具函数
 */

// 时间格式化
export const formatTime = (timestamp: number, format: string = 'YYYY-MM-DD HH:mm'): string => {
  const date = new Date(timestamp)
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year.toString())
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

// 相对时间格式化
export const formatRelativeTime = (timestamp: number): string => {
  const now = Date.now()
  const diff = now - timestamp
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`
  } else {
    return formatTime(timestamp, 'YYYY-MM-DD')
  }
}

// 文本截断
export const truncateText = (text: string, maxLength: number = 100): string => {
  if (text.length <= maxLength) {
    return text
  }
  return text.substring(0, maxLength) + '...'
}

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: number | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout !== null) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let lastTime = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastTime >= wait) {
      lastTime = now
      func(...args)
    }
  }
}

// 图片压缩
export const compressImage = (filePath: string, quality: number = 0.8): Promise<string> => {
  return new Promise((resolve, reject) => {
    uni.compressImage({
      src: filePath,
      quality,
      success: (res) => {
        resolve(res.tempFilePath)
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

// 获取图片信息
export const getImageInfo = (src: string): Promise<UniApp.GetImageInfoSuccessData> => {
  return new Promise((resolve, reject) => {
    uni.getImageInfo({
      src,
      success: (res) => {
        resolve(res)
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

// 复制到剪贴板
export const copyToClipboard = (text: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    uni.setClipboardData({
      data: text,
      success: () => {
        uni.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        })
        resolve()
      },
      fail: (err) => {
        uni.showToast({
          title: '复制失败',
          icon: 'none'
        })
        reject(err)
      }
    })
  })
}

// 显示确认对话框
export const showConfirm = (
  title: string,
  content: string,
  confirmText: string = '确定',
  cancelText: string = '取消'
): Promise<boolean> => {
  return new Promise((resolve) => {
    uni.showModal({
      title,
      content,
      confirmText,
      cancelText,
      success: (res) => {
        resolve(res.confirm)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

// 显示操作菜单
export const showActionSheet = (itemList: string[]): Promise<number> => {
  return new Promise((resolve, reject) => {
    uni.showActionSheet({
      itemList,
      success: (res) => {
        resolve(res.tapIndex)
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

// 获取系统信息
export const getSystemInfo = (): Promise<UniApp.GetSystemInfoResult> => {
  return new Promise((resolve, reject) => {
    uni.getSystemInfo({
      success: (res) => {
        resolve(res)
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

// 检查网络状态
export const checkNetworkStatus = (): Promise<UniApp.GetNetworkTypeResult> => {
  return new Promise((resolve, reject) => {
    uni.getNetworkType({
      success: (res) => {
        resolve(res)
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

// 生成唯一ID
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 验证文本内容
export const validateText = (text: string): { valid: boolean; message?: string } => {
  if (!text || text.trim().length === 0) {
    return { valid: false, message: '请输入要核查的内容' }
  }
  
  if (text.length < 5) {
    return { valid: false, message: '输入内容过短，请输入至少5个字符' }
  }
  
  if (text.length > 2000) {
    return { valid: false, message: '输入内容过长，请控制在2000字符以内' }
  }
  
  return { valid: true }
}

// 验证图片文件
export const validateImage = (filePath: string): Promise<{ valid: boolean; message?: string }> => {
  return new Promise(async (resolve) => {
    try {
      const imageInfo = await getImageInfo(filePath)
      
      // 检查文件大小（限制为10MB）
      const maxSize = 10 * 1024 * 1024
      if (imageInfo.width * imageInfo.height > maxSize) {
        resolve({ valid: false, message: '图片文件过大，请选择小于10MB的图片' })
        return
      }
      
      // 检查图片尺寸
      if (imageInfo.width < 100 || imageInfo.height < 100) {
        resolve({ valid: false, message: '图片尺寸过小，请选择清晰的图片' })
        return
      }
      
      resolve({ valid: true })
    } catch (error) {
      resolve({ valid: false, message: '图片格式不支持或文件损坏' })
    }
  })
}
