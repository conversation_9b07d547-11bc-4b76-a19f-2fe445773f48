<template>
	<view class="container">
		<uni-card :is-shadow="false" is-full>
			<text class="uni-h6">应用设置</text>
			
			<!-- 历史记录设置 -->
			<uni-section title="历史记录" type="line">
				<uni-list>
					<uni-list-item 
						title="自动保存历史记录" 
						:switch-checked="settings.autoSaveHistory"
						:show-switch="true"
						@switchChange="handleSwitchChange('autoSaveHistory', $event)"
					/>
					<uni-list-item 
						title="最大历史记录数量" 
						:right-text="settings.maxHistoryCount + '条'"
						show-arrow
						@click="showHistoryCountPicker"
					/>
				</uni-list>
			</uni-section>
			
			<!-- 通知设置 -->
			<uni-section title="通知设置" type="line">
				<uni-list>
					<uni-list-item 
						title="启用通知提醒" 
						:switch-checked="settings.enableNotification"
						:show-switch="true"
						@switchChange="handleSwitchChange('enableNotification', $event)"
					/>
				</uni-list>
			</uni-section>
			
			<!-- 数据管理 -->
			<uni-section title="数据管理" type="line">
				<uni-list>
					<uni-list-item 
						title="清空历史记录" 
						:right-text="historyCount + '条记录'"
						show-arrow
						@click="showClearHistoryConfirm"
					/>
					<uni-list-item 
						title="清空所有数据" 
						show-arrow
						@click="showClearAllDataConfirm"
					/>
				</uni-list>
			</uni-section>
			
			<!-- 关于信息 -->
			<uni-section title="关于" type="line">
				<uni-list>
					<uni-list-item 
						title="应用版本" 
						:right-text="appVersion"
					/>
					<uni-list-item 
						title="存储使用情况" 
						:right-text="storageInfo"
						show-arrow
						@click="showStorageInfo"
					/>
				</uni-list>
			</uni-section>
		</uni-card>
		
		<!-- 历史记录数量选择器 -->
		<uni-popup ref="historyCountPopup" type="bottom">
			<uni-picker-view 
				:value="[historyCountIndex]" 
				@change="onHistoryCountChange"
				class="picker-view"
			>
				<uni-picker-view-column>
					<view 
						v-for="(count, index) in historyCountOptions" 
						:key="index"
						class="picker-item"
					>
						{{ count }}条
					</view>
				</uni-picker-view-column>
			</uni-picker-view>
			<view class="picker-actions">
				<button @click="cancelHistoryCountPicker">取消</button>
				<button type="primary" @click="confirmHistoryCountPicker">确定</button>
			</view>
		</uni-popup>
	</view>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted } from 'vue'
import { settingsManager, historyManager, storageUtils } from '@/utils/storage'
import { showConfirm } from '@/utils/common'

export default defineComponent({
	name: 'Settings',
	setup() {
		// 设置数据
		const settings = ref(settingsManager.getSettings())
		
		// 历史记录数量选项
		const historyCountOptions = [50, 100, 200, 500, 1000]
		const historyCountIndex = ref(0)
		const historyCountPopup = ref<any>(null)
		
		// 应用版本
		const appVersion = ref('1.0.0')
		
		// 历史记录数量
		const historyCount = computed(() => {
			return historyManager.getHistory().length
		})
		
		// 存储信息
		const storageInfo = computed(() => {
			const info = storageUtils.getInfo()
			const usedMB = (info.currentSize / 1024).toFixed(2)
			const totalMB = (info.limitSize / 1024).toFixed(2)
			return `${usedMB}MB / ${totalMB}MB`
		})
		
		// 初始化历史记录数量索引
		onMounted(() => {
			const currentCount = settings.value.maxHistoryCount
			historyCountIndex.value = historyCountOptions.findIndex(count => count === currentCount)
			if (historyCountIndex.value === -1) {
				historyCountIndex.value = 1 // 默认100条
			}
		})
		
		// 处理开关变化
		const handleSwitchChange = (key: string, event: any) => {
			const value = event.detail.value
			settings.value[key] = value
			settingsManager.updateSettings({ [key]: value })
			
			uni.showToast({
				title: '设置已保存',
				icon: 'success'
			})
		}
		
		// 显示历史记录数量选择器
		const showHistoryCountPicker = () => {
			if (historyCountPopup.value) {
				historyCountPopup.value.open()
			}
		}
		
		// 历史记录数量变化
		const onHistoryCountChange = (e: any) => {
			historyCountIndex.value = e.detail.value[0]
		}
		
		// 取消历史记录数量选择
		const cancelHistoryCountPicker = () => {
			if (historyCountPopup.value) {
				historyCountPopup.value.close()
			}
		}
		
		// 确认历史记录数量选择
		const confirmHistoryCountPicker = () => {
			const newCount = historyCountOptions[historyCountIndex.value]
			settings.value.maxHistoryCount = newCount
			settingsManager.updateSettings({ maxHistoryCount: newCount })
			
			if (historyCountPopup.value) {
				historyCountPopup.value.close()
			}
			
			uni.showToast({
				title: '设置已保存',
				icon: 'success'
			})
		}
		
		// 显示清空历史记录确认
		const showClearHistoryConfirm = async () => {
			const confirmed = await showConfirm(
				'清空历史记录',
				`确定要清空所有 ${historyCount.value} 条历史记录吗？此操作不可恢复。`,
				'清空',
				'取消'
			)
			
			if (confirmed) {
				historyManager.clearHistory()
				uni.showToast({
					title: '历史记录已清空',
					icon: 'success'
				})
			}
		}
		
		// 显示清空所有数据确认
		const showClearAllDataConfirm = async () => {
			const confirmed = await showConfirm(
				'清空所有数据',
				'确定要清空所有应用数据吗？包括历史记录、设置等。此操作不可恢复。',
				'清空',
				'取消'
			)
			
			if (confirmed) {
				storageUtils.clear()
				settings.value = settingsManager.getSettings() // 重新加载默认设置
				uni.showToast({
					title: '所有数据已清空',
					icon: 'success'
				})
			}
		}
		
		// 显示存储信息
		const showStorageInfo = () => {
			const info = storageUtils.getInfo()
			const usedMB = (info.currentSize / 1024).toFixed(2)
			const totalMB = (info.limitSize / 1024).toFixed(2)
			const usagePercent = ((info.currentSize / info.limitSize) * 100).toFixed(1)
			
			uni.showModal({
				title: '存储使用情况',
				content: `已使用: ${usedMB}MB\n总容量: ${totalMB}MB\n使用率: ${usagePercent}%\n\n存储的数据类型:\n• 历史记录: ${historyCount.value}条\n• 应用设置\n• 缓存数据`,
				showCancel: false,
				confirmText: '知道了'
			})
		}
		
		return {
			settings,
			historyCountOptions,
			historyCountIndex,
			historyCountPopup,
			appVersion,
			historyCount,
			storageInfo,
			handleSwitchChange,
			showHistoryCountPicker,
			onHistoryCountChange,
			cancelHistoryCountPicker,
			confirmHistoryCountPicker,
			showClearHistoryConfirm,
			showClearAllDataConfirm,
			showStorageInfo
		}
	}
})
</script>

<style lang="scss">
.container {
	padding: 20rpx;
}

.picker-view {
	height: 400rpx;
	background-color: #FFFFFF;
}

.picker-item {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 80rpx;
	font-size: 32rpx;
}

.picker-actions {
	display: flex;
	background-color: #FFFFFF;
	border-top: 1rpx solid #E5E5E5;
	
	button {
		flex: 1;
		margin: 20rpx;
		border-radius: 8rpx;
	}
}
</style>
