// 用户反馈云函数
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { check_id, rating, comment = '' } = event
    
    // 参数验证
    if (!check_id) {
      return {
        code: 400,
        message: '核查记录ID不能为空',
        error: 'INVALID_CHECK_ID'
      }
    }
    
    if (!rating || !['accurate', 'inaccurate'].includes(rating)) {
      return {
        code: 400,
        message: '评价结果无效，必须是 accurate 或 inaccurate',
        error: 'INVALID_RATING'
      }
    }
    
    const db = cloud.database()
    
    // 检查核查记录是否存在
    const checkRecord = await db.collection('check_records')
      .where({
        id: check_id
      })
      .get()
    
    if (checkRecord.data.length === 0) {
      return {
        code: 404,
        message: '核查记录不存在',
        error: 'CHECK_RECORD_NOT_FOUND'
      }
    }
    
    // 生成反馈ID
    const feedbackId = `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // 保存用户反馈
    await db.collection('user_feedback').add({
      data: {
        id: feedbackId,
        check_id: check_id,
        rating: rating,
        comment: comment,
        user_openid: wxContext.OPENID,
        created_at: new Date()
      }
    })
    
    // 更新核查记录的反馈统计
    const record = checkRecord.data[0]
    const feedbackStats = record.feedback_stats || { accurate: 0, inaccurate: 0, total: 0 }
    
    feedbackStats[rating] = (feedbackStats[rating] || 0) + 1
    feedbackStats.total = feedbackStats.total + 1
    
    await db.collection('check_records')
      .doc(record._id)
      .update({
        data: {
          feedback_stats: feedbackStats,
          updated_at: new Date()
        }
      })
    
    return {
      code: 200,
      message: '反馈提交成功',
      data: {
        feedback_id: feedbackId,
        feedback_stats: feedbackStats
      }
    }
    
  } catch (error) {
    console.error('提交反馈失败:', error)
    
    return {
      code: 500,
      message: '提交反馈失败，请稍后重试',
      error: 'SERVICE_UNAVAILABLE'
    }
  }
}
