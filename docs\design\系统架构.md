## **AI赋能群聊信息核查助手 - 系统概要设计文档**

### **1. 引言**

本文档基于《AI赋能群聊信息核查助手 - 产品需求文档 (PRD)》，旨在对“AI赋能群聊信息核查助手”小程序进行概要设计。本文档将概述系统架构、关键模块设计、基于uni-app框架的技术选型以及与PRD需求的对应关系，为后续的详细设计和开发工作提供指导。

### **2. 系统概述**

本产品是一款基于AI技术（OCR、LLM）的小程序，旨在帮助用户快速提取、分析并核查微信、QQ等群聊中接收到的文本及图片信息（尤其是聊天截图和新闻类图片），判断其真实性并追溯原始发布时间。系统将采用前后端分离的架构，前端使用uni-app框架开发，以实现跨平台（微信小程序、QQ小程序）部署。后端负责核心业务逻辑处理、AI能力集成和数据管理。

### **3. 系统架构**

```
graph TD

    subgraph "用户端_Uni_app小程序"
        U[用户界面与交互]
        IS[信息输入模块_FR1]
        CDS[内容展示模块_FR4]
        FB[反馈模块_FR5]
        HS[历史记录模块_FR6]
        LSM[本地存储_uni_storage]
    end

    subgraph "后端服务_Serverless微服务"
        API[API网关]
        CM[配置管理模块_FR7_4]
        FBC[反馈收集模块_FR7_5]
        BA[业务逻辑与编排]
        DB[数据库_用户信息配置反馈]
    end

    subgraph "AI与第三方服务"
        OCR_SERVICE[OCR服务_FR7_1_6_2]
        LLM_SERVICE[LLM服务_RAG_FR7_2_6_3]
        SEARCH_SERVICE[网络搜索服务_FR7_3_6_6]
    end

    U --> IS
    IS -->|图片文本| API
    API -->|原始信息| BA
    BA -->|提取请求| OCR_SERVICE
    OCR_SERVICE -->|提取文本| BA
    BA -->|理解摘要搜索查询生成| LLM_SERVICE
    LLM_SERVICE -->|查询| SEARCH_SERVICE
    SEARCH_SERVICE -->|搜索结果| LLM_SERVICE
    LLM_SERVICE -->|分析判断溯源时间估算| BA
    BA -->|核查结果| API
    API -->|核查结果| CDS
    U --> FB
    FB -->|反馈数据| API
    API -->|反馈数据| FBC
    FBC --> DB
    U --> HS
    HS -->|读写| LSM
    CM --> BA
    BA --> DB

    classDef frontend fill:#D6EAF8,stroke:#3498DB,stroke-width:2px
    classDef backend fill:#D5F5E3,stroke:#2ECC71,stroke-width:2px
    classDef thirdparty fill:#FCF3CF,stroke:#F1C40F,stroke-width:2px

    class U,IS,CDS,FB,HS,LSM frontend
    class API,CM,FBC,BA,DB backend
    class OCR_SERVICE,LLM_SERVICE,SEARCH_SERVICE thirdparty
```

**组件说明:**

* **用户端 (Uni-app 小程序):**  
  * **用户界面与交互 (U):** 基于uni-app框架构建，负责视图渲染和用户操作响应。使用Vue.js语法，可选用uni-ui等组件库。  
  * **信息输入模块 (IS - 对应PRD FR1):**  
    * **文本输入 (FR1.1):** 提供组件供用户粘贴内容。  
    * **图片上传 (从相册 FR1.2):** 调用 uni.chooseImage API，允许用户从相册选择图片或拍照。  
    * **图片上传 (从聊天会话 - QQ平台优先 FR1.3):**  
      * QQ端：通过条件编译 (#ifdef MP-QQ) 调用QQ小程序原生API qq.chooseMessageFile({ type: 'image', ... }) 来实现从聊天会话选择图片。  
      * 微信端：鉴于API限制，初期引导用户先保存图片至相册，再通过FR1.2方式上传。  
  * **内容展示模块 (CDS - FR4):** 展示OCR识别结果（可选，FR2.2）、AI分析的综合评估结果、来源链接、预估发布时间等。  
  * **反馈模块 (FB - FR5):** 允许用户对核查结果进行简单评价（如“准确”/“不准确”），数据通过API发送至后端。  
  * **历史记录模块 (HS - FR6):**  
    * 使用 uni.setStorageSync 和 uni.getStorageSync (或异步的 uni.setStorage / uni.getStorage) API 将用户最近的核查历史（如查询内容、简要结果）存储在小程序本地。需注意各平台本地存储的大小限制（如微信小程序单个key最大1MB，总共10MB）。  
  * **本地存储 (LSM):** uni-app提供的本地数据缓存能力。  
* **后端服务:**  
  * **API网关 (API):** 统一接收前端请求，进行鉴权、路由和限流。  
  * **业务逻辑与编排 (BA):** 核心模块，负责接收前端请求，按顺序调用OCR、LLM、网络搜索等服务，完成信息的核查与分析流程。  
  * **配置管理模块 (CM - FR7.4):** 用于管理AI模型的提示词、版本、可信来源权重等可配置参数。  
  * **反馈收集模块 (FBC - FR7.5):** 收集并存储用户通过小程序提交的反馈数据。  
  * **数据库 (DB - 6.5):** 用于存储用户信息（如用户授权状态、偏好设置）、用户反馈数据。根据PRD，核查历史主要在本地，若用户同意或用于模型优化（需匿名化处理），可考虑存储部分。  
* **AI与第三方服务:**  
  * **OCR服务 (OCR_SERVICE - FR7.1, 6.2):**  
    * 接收图片数据，返回识别后的文本信息。  
    * 后端封装对云端OCR服务（如腾讯云OCR）的调用。PRD FR7.1明确为后端服务接口。  
  * **LLM服务 (LLM_SERVICE - FR7.2, 6.3):**  
    * 接收文本信息（来自用户输入或OCR结果），执行内容理解、摘要生成、搜索查询构建、信息比对、真伪判断、来源追溯、发布时间估算等任务。  
    * 采用PRD建议的RAG（检索增强生成）架构，结合外部网络搜索能力。  
  * **网络搜索服务 (SEARCH_SERVICE - FR7.3, 6.6):** 接收LLM生成的查询语句，通过第三方搜索引擎API进行网络内容检索。

### **4. 模块设计概要**

#### **4.1. 前端模块 (uni-app)**

* **页面结构 (Pages):**  
  * pages/index/index: 首页，提供文本输入框、图片上传按钮（根据平台条件编译显示不同上传方式，如QQ的“从聊天选择”）。  
  * pages/result/result: 结果展示页，清晰展示FR4.1中定义的各项核查结果。  
  * pages/history/history: 历史记录页，列表展示本地存储的核查记录 (FR6.1)。  
  * pages/loading/loading (或作为组件): 处理中页面，显示加载状态和友好提示。  
* **核心API与组件使用:**  
  * **网络请求:** uni.request 用于与后端API进行数据交互。  
  * **图片选择:**  
    * uni.chooseImage({ sourceType: ['album', 'camera'], ... }): 从相册选择或拍照 (FR1.2)。  
    * 条件编译调用 qq.chooseMessageFile (FR1.3 - QQ)。  
  * **本地存储:** uni.setStorageSync, uni.getStorageSync, uni.removeStorageSync, uni.clearStorageSync 用于管理历史记录。  
  * **用户反馈:** 简单的按钮交互，将评价结果通过 uni.request 发送。  
  * **OCR结果修正 (FR2.2 - 可选/低置信度时):** 若实现，前端需提供文本编辑界面，允许用户修改OCR提取的文本，再提交给后端进行分析。  
* **状态管理:**  
  * 对于简单应用，可使用Vue的原生响应式能力或uni-app提供的globalData。  
  * 若应用复杂度增加，可考虑引入Vuex的uni-app适配版 (vuex)进行更集中的状态管理。  
* **组件化:**  
  * 将可复用的UI元素封装成自定义组件，如结果展示卡片、加载提示组件等，提高代码复用性和可维护性。  
  * 可考虑使用 uni-ui 扩展组件库加速开发。  
* **条件编译:** 广泛应用于处理平台差异，特别是API调用和UI展示的细微差别（如FR1.3的QQ图片选择）。

#### **4.2. 后端模块**

* **API接口设计 (RESTful):**  
  * POST /api/v1/check/text: 接收用户直接输入的文本进行核查。  
    * Request Body: { "text": "待核查文本" }  
  * POST /api/v1/check/image: 接收用户上传的图片文件进行核查（后端处理OCR）。  
    * Request Body: (multipart/form-data) image_file  
  * POST /api/v1/feedback: 接收用户对核查结果的反馈。  
    * Request Body: { "check_id": "某次核查的唯一标识", "rating": "accurate/inaccurate" }  
  * (可选，若服务端也存历史) GET /api/v1/history: 获取用户云端历史。  
* **信息核查核心流程 (对应PRD FR3):**  
  1. **输入接收与预处理:** API接收前端数据。若是图片，先进行存储或直接传递给OCR服务。  
  2. **内容提取 (FR2.1):** 若输入为图片，调用OCR服务，获取图片中的文本信息。  
  3. **文本理解与摘要 (FR3.1):** 将获取的文本（用户输入或OCR结果）传递给LLM服务，进行关键信息提取、实体识别和内容摘要。  
  4. **网络搜索查询构建与执行 (FR3.2):** LLM根据理解后的文本内容，生成针对性的搜索查询，调用网络搜索服务接口获取相关信息。  
  5. **真伪判断与可信度评估 (FR3.3):** LLM分析网络搜索结果，与原始信息进行比对，评估其真实性。  
  6. **原始来源追溯 (FR3.4):** LLM尝试从搜索结果中识别并判定信息的原始或早期可信来源。  
  7. **原始发布时间估算 (FR3.5):** LLM分析各搜索结果中的时间戳、元数据等信息，结合来源可信度，估算信息的原始发布日期或时间范围。  
  8. **结果组装与返回:** 将上述分析结果（真实性指示、摘要、预估时间、主要来源链接、佐证材料链接等）格式化后，通过API返回给前端。  
* **技术选型考量:**  
  * **运行时环境:** Node.js (配合Express.js/Koa.js) 或 Python (配合Flask/Django)。  
  * **部署方案:** 考虑使用Serverless架构（如腾讯云云函数SCF、阿里云函数计算FC）以实现弹性伸缩和按需付费，有效应对并发请求 (NFR1.2)。或者使用容器化部署（Docker + Kubernetes）。

#### **4.3. AI与第三方服务集成**

* **OCR服务集成:** 后端通过SDK或HTTP API方式调用选定的云端OCR服务。需妥善管理API密钥，并实现错误处理和重试机制。  
* **LLM服务集成:** 后端通过SDK或HTTP API方式调用LLM服务。核心在于设计高效的提示工程 (Prompt Engineering) 以满足PRD中FR3.1至FR3.5的各项分析需求，并有效实施RAG架构 (NFR2.2)。  
* **网络搜索服务集成:** 后端通过SDK或HTTP API方式调用集成的搜索引擎API。

### **5. 非功能需求实现策略 (对应PRD 5)**

* **性能 (NFR1.1 响应速度, NFR1.2 并发处理):**  
  * **前端 (uni-app):** 代码分包加载、图片懒加载与压缩、骨架屏或加载动画提升用户感知性能。  
  * **后端:** 优化核心算法效率，对AI服务调用采用异步处理机制，缓存可复用的中间结果或常用查询结果，选择高性能的Serverless或容器化部署方案。  
  * **AI服务:** 选择响应速度较快的模型版本和API。  
* **准确性与可靠性 (NFR2):**  
  * **OCR准确率 (NFR2.1):** 选用PRD推荐的高准确率云端OCR服务，对上传图片进行预处理（如提示用户上传清晰图片）。  
  * **LLM分析可靠性 (NFR2.2):** 通过精细化的提示工程、RAG架构、多源信息交叉验证等手段，减少LLM产生幻觉的概率。对不确定性较高的结果进行明确标注。  
  * **来源追溯与日期判断 (NFR2.3):** 结合多种信号源，力求准确，对结果的置信度进行评估和展示。  
* **易用性 (NFR3):**  
  * **前端 (uni-app):** 遵循uni-app的设计规范和目标平台（微信/QQ小程序）的交互习惯，确保操作流程简洁 (NFR3.1)，结果展示直观易懂 (NFR3.2)，特别是考虑到中老年用户的理解能力。  
  * 利用uni-app的跨端能力，尽可能保证核心体验一致性，对平台差异导致的功能不同点进行清晰说明 (NFR3.3)。  
* **安全性 (NFR4):**  
  * **数据传输 (NFR4.1):** 前后端通讯全程使用HTTPS加密。  
  * **后端服务 (NFR4.2):** API接口进行严格的身份验证和权限控制（如使用JWT），防止未授权访问和恶意攻击。对用户输入进行必要的安全过滤。  
  * **依赖服务 (NFR4.3):** 安全管理第三方API密钥，监控其安全性。  
* **隐私性 (NFR5):**  
  * **用户同意 (NFR5.1):** 在首次使用或涉及敏感数据（如图片上传）前，通过弹窗等方式清晰告知用户数据用途，并获取明确授权。  
  * **数据最小化 (NFR5.2):** 仅收集和处理与核心核查功能直接相关的必要信息。  
  * **数据存储与删除 (NFR5.3):** 用户上传的原始图片和文本内容，在完成分析后，除非用户明确同意或用于模型优化（需进行彻底匿名化处理），否则不应在后端长期保存。本地历史记录应允许用户清除。  
  * **匿名化与聚合 (NFR5.4):** 若收集数据用于模型改进，必须进行彻底的匿名化和聚合处理。  
* **合规性 (NFR6):**  
  * 严格遵守微信和QQ小程序平台的开发者协议、运营规范、数据隐私和内容安全政策 (NFR6.1)。  
  * 对AI生成的结果进行风险评估和提示，避免小程序成为新的不实信息传播源。对用户输入内容进行基本的敏感词过滤 (NFR6.2)。

### **6. MVP (最小可行产品) 范围技术实现 (对应PRD 7)**

* **前端 (uni-app):**  
  * 实现核心的图片输入（微信端：从相册/相机；QQ端：优先从相册/相机，若qq.chooseMessageFile快速可行则包含）和文本输入界面。  
  * 调用后端API进行核查，并展示基础的加载状态。  
  * 清晰展示最核心的核查结果：基础真实性评估（如：有证据支持/有证据反驳/信息不足）、1-2个关键来源链接、初步的原始发布日期估算（若能较有把握地找到）。  
* **后端:**  
  * 实现 /api/v1/check/text 和 /api/v1/check/image 核心API。  
  * 集成OCR服务（处理图片输入）。  
  * 集成LLM服务，实现基于提取文本的RAG驱动网络搜索。  
  * 实现基础的真实性评估逻辑和来源/日期提取逻辑。  
* **技术验证重点:** 验证OCR-LLM-搜索-结果呈现的核心链路的通畅性、基本准确性和性能表现。

### **7. 未来迭代方向技术考量 (对应PRD 8)**

* **功能增强:**  
  * **前端:** 支持更复杂的反馈机制，展示信源可信度分析。  
  * **后端:** 引入更精细化的LLM分析模块（如断章取义识别、讽刺内容识别），构建信源可信度数据库和评估模型，支持对已核查信息的状态更新推送（可能需要WebSocket或轮询）。  
* **体验优化:**  
  * **前端:** 针对特定用户群体的无障碍设计优化 (uni-app 支持部分无障碍特性)。  
* **技术升级:**  
  * 持续跟进并评估引入更先进的OCR和LLM模型。  
  * 优化RAG策略，例如更智能的文档分块、向量检索和重排技术。  
  * 探索AI在识别深度伪造内容方面的应用集成。  
* **生态扩展:**  
  * 若与官方辟谣平台合作，后端需开发相应的数据接口对接。

此概要设计文档为后续详细设计和开发奠定了基础。在开发过程中，需持续关注uni-app官方文档和社区更新，以及各小程序平台的最新政策和API变化。