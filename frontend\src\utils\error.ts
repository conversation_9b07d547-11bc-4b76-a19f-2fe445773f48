/**
 * 错误处理工具
 */

// 错误类型枚举
export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  STORAGE_ERROR = 'STORAGE_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// 自定义错误类
export class AppError extends Error {
  public type: ErrorType
  public code?: string | number
  public details?: any

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN_ERROR,
    code?: string | number,
    details?: any
  ) {
    super(message)
    this.name = 'AppError'
    this.type = type
    this.code = code
    this.details = details
  }
}

// 错误信息映射
const ERROR_MESSAGES = {
  [ErrorType.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [ErrorType.API_ERROR]: '服务器响应异常，请稍后重试',
  [ErrorType.VALIDATION_ERROR]: '输入数据格式不正确',
  [ErrorType.STORAGE_ERROR]: '本地存储操作失败',
  [ErrorType.PERMISSION_ERROR]: '权限不足，无法完成操作',
  [ErrorType.UNKNOWN_ERROR]: '未知错误，请稍后重试'
}

// 错误处理器
export class ErrorHandler {
  // 处理错误并显示用户友好的提示
  static handle(error: any, showToast: boolean = true): void {
    console.error('Error occurred:', error)
    
    let message: string
    let type: ErrorType = ErrorType.UNKNOWN_ERROR
    
    if (error instanceof AppError) {
      message = error.message
      type = error.type
    } else if (error.errMsg) {
      // uni-app API 错误
      message = this.parseUniAppError(error.errMsg)
      type = this.getErrorTypeFromUniApp(error.errMsg)
    } else if (error.message) {
      message = error.message
    } else if (typeof error === 'string') {
      message = error
    } else {
      message = ERROR_MESSAGES[ErrorType.UNKNOWN_ERROR]
    }
    
    // 记录错误日志
    this.logError(error, type)
    
    // 显示用户提示
    if (showToast) {
      uni.showToast({
        title: message,
        icon: 'none',
        duration: 3000
      })
    }
  }
  
  // 解析uni-app错误信息
  private static parseUniAppError(errMsg: string): string {
    if (errMsg.includes('request:fail')) {
      return '网络请求失败，请检查网络连接'
    } else if (errMsg.includes('timeout')) {
      return '请求超时，请稍后重试'
    } else if (errMsg.includes('abort')) {
      return '请求被取消'
    } else if (errMsg.includes('fail')) {
      return '操作失败，请重试'
    }
    return errMsg
  }
  
  // 从uni-app错误信息获取错误类型
  private static getErrorTypeFromUniApp(errMsg: string): ErrorType {
    if (errMsg.includes('request:fail') || errMsg.includes('timeout')) {
      return ErrorType.NETWORK_ERROR
    } else if (errMsg.includes('permission')) {
      return ErrorType.PERMISSION_ERROR
    }
    return ErrorType.UNKNOWN_ERROR
  }
  
  // 记录错误日志
  private static logError(error: any, type: ErrorType): void {
    const errorLog = {
      timestamp: new Date().toISOString(),
      type,
      message: error.message || error.errMsg || String(error),
      stack: error.stack,
      userAgent: navigator.userAgent,
      url: window.location?.href || 'unknown'
    }
    
    // 在开发环境下打印详细错误信息
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Error Details')
      console.error('Type:', type)
      console.error('Message:', errorLog.message)
      console.error('Stack:', errorLog.stack)
      console.error('Full Error:', error)
      console.groupEnd()
    }
    
    // 在生产环境下可以发送错误日志到服务器
    // this.sendErrorToServer(errorLog)
  }
  
  // 发送错误日志到服务器（可选实现）
  private static sendErrorToServer(errorLog: any): void {
    // 实现错误日志上报逻辑
    // 可以使用uni.request发送到错误收集服务
  }
}

// 网络错误处理
export const handleNetworkError = (error: any): void => {
  if (error.errMsg?.includes('timeout')) {
    ErrorHandler.handle(new AppError('请求超时，请检查网络连接', ErrorType.NETWORK_ERROR))
  } else if (error.errMsg?.includes('fail')) {
    ErrorHandler.handle(new AppError('网络连接失败，请稍后重试', ErrorType.NETWORK_ERROR))
  } else {
    ErrorHandler.handle(error)
  }
}

// API错误处理
export const handleApiError = (error: any, statusCode?: number): void => {
  let message = '服务器响应异常'
  
  if (statusCode) {
    switch (statusCode) {
      case 400:
        message = '请求参数错误'
        break
      case 401:
        message = '未授权，请重新登录'
        break
      case 403:
        message = '权限不足'
        break
      case 404:
        message = '请求的资源不存在'
        break
      case 500:
        message = '服务器内部错误'
        break
      case 502:
        message = '网关错误'
        break
      case 503:
        message = '服务暂时不可用'
        break
      default:
        message = `服务器错误 (${statusCode})`
    }
  }
  
  ErrorHandler.handle(new AppError(message, ErrorType.API_ERROR, statusCode))
}

// 验证错误处理
export const handleValidationError = (message: string, details?: any): void => {
  ErrorHandler.handle(new AppError(message, ErrorType.VALIDATION_ERROR, undefined, details))
}

// 存储错误处理
export const handleStorageError = (error: any): void => {
  ErrorHandler.handle(new AppError('本地存储操作失败', ErrorType.STORAGE_ERROR, undefined, error))
}

// 权限错误处理
export const handlePermissionError = (message: string = '权限不足'): void => {
  ErrorHandler.handle(new AppError(message, ErrorType.PERMISSION_ERROR))
}

// 全局错误处理器
export const setupGlobalErrorHandler = (): void => {
  // 监听未捕获的Promise错误
  window.addEventListener?.('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    ErrorHandler.handle(event.reason, false) // 不显示toast，避免干扰用户
    event.preventDefault()
  })
  
  // 监听全局错误
  window.addEventListener?.('error', (event) => {
    console.error('Global error:', event.error)
    ErrorHandler.handle(event.error, false) // 不显示toast，避免干扰用户
  })
}
