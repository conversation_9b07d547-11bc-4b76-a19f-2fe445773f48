#!/usr/bin/env node

/**
 * 项目结构重构脚本
 * 将当前的uni-app + 云开发混合结构重构为标准的前后端分离结构
 */

const fs = require('fs')
const path = require('path')

console.log('🚀 开始项目结构重构...\n')

// 1. 创建新的目录结构
console.log('📁 创建目录结构...')
const directories = [
  'frontend',
  'frontend/src',
  'backend',
  'backend/cloudfunctions',
  'backend/database',
  'backend/database/schemas',
  'backend/database/migrations',
  'backend/config',
  'backend/scripts',
  'docs',
  'docs/api',
  'docs/deployment',
  'docs/development',
  'docs/design',
  'tools',
  'tests',
  'tests/frontend',
  'tests/backend',
  'tests/e2e',
  '.vscode'
]

directories.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true })
    console.log(`  ✅ 创建目录: ${dir}`)
  } else {
    console.log(`  ⚠️  目录已存在: ${dir}`)
  }
})

// 2. 移动前端文件
console.log('\n📦 移动前端文件...')
const frontendFiles = [
  { from: 'src', to: 'frontend/src' },
  { from: 'package.json', to: 'frontend/package.json' },
  { from: 'vite.config.ts', to: 'frontend/vite.config.ts' },
  { from: 'tsconfig.json', to: 'frontend/tsconfig.json' },
  { from: 'manifest.json', to: 'frontend/manifest.json' },
  { from: 'project.config.json', to: 'frontend/project.config.json' }
]

frontendFiles.forEach(({ from, to }) => {
  if (fs.existsSync(from)) {
    try {
      if (fs.existsSync(to)) {
        console.log(`  ⚠️  目标文件已存在，跳过: ${from} -> ${to}`)
      } else {
        fs.renameSync(from, to)
        console.log(`  ✅ 移动文件: ${from} -> ${to}`)
      }
    } catch (error) {
      console.log(`  ❌ 移动失败: ${from} -> ${to} (${error.message})`)
    }
  } else {
    console.log(`  ⚠️  源文件不存在: ${from}`)
  }
})

// 3. 移动后端文件
console.log('\n🔧 移动后端文件...')
if (fs.existsSync('cloudfunctions')) {
  try {
    if (fs.existsSync('backend/cloudfunctions')) {
      console.log('  ⚠️  backend/cloudfunctions 已存在，请手动处理')
    } else {
      fs.renameSync('cloudfunctions', 'backend/cloudfunctions')
      console.log('  ✅ 移动云函数目录: cloudfunctions -> backend/cloudfunctions')
    }
  } catch (error) {
    console.log(`  ❌ 移动云函数目录失败: ${error.message}`)
  }
} else {
  console.log('  ⚠️  cloudfunctions 目录不存在')
}

// 4. 移动文档文件
console.log('\n📚 移动文档文件...')
const docFiles = [
  { from: 'docs/API接口文档.md', to: 'docs/api/API接口文档.md' },
  { from: 'docs/微信云开发部署指南.md', to: 'docs/deployment/微信云开发部署指南.md' },
  { from: '微信云开发快速开始.md', to: 'docs/deployment/快速开始.md' },
  { from: '微信云开发后端框架总结.md', to: 'docs/development/后端开发指南.md' },
  { from: '项目实现总结.md', to: 'docs/development/前端开发指南.md' },
  { from: '后端API需求总结.md', to: 'docs/design/API设计.md' },
  { from: 'docs/design.md', to: 'docs/design/系统架构.md' }
]

docFiles.forEach(({ from, to }) => {
  if (fs.existsSync(from)) {
    try {
      // 确保目标目录存在
      const targetDir = path.dirname(to)
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true })
      }
      
      if (fs.existsSync(to)) {
        console.log(`  ⚠️  目标文件已存在，跳过: ${from} -> ${to}`)
      } else {
        fs.renameSync(from, to)
        console.log(`  ✅ 移动文档: ${from} -> ${to}`)
      }
    } catch (error) {
      console.log(`  ❌ 移动失败: ${from} -> ${to} (${error.message})`)
    }
  } else {
    console.log(`  ⚠️  源文件不存在: ${from}`)
  }
})

// 5. 创建新的配置文件
console.log('\n⚙️  创建配置文件...')

// 根目录 package.json
const rootPackageJson = {
  "name": "news-verifier-ai",
  "version": "1.0.0",
  "description": "AI赋能群聊信息核查助手",
  "scripts": {
    "dev:frontend": "cd frontend && npm run dev",
    "build:frontend": "cd frontend && npm run build",
    "deploy:backend": "cd backend && npm run deploy",
    "deploy:all": "npm run build:frontend && npm run deploy:backend",
    "test": "npm run test:frontend && npm run test:backend",
    "test:frontend": "cd frontend && npm run test",
    "test:backend": "cd backend && npm run test",
    "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install"
  },
  "workspaces": [
    "frontend",
    "backend"
  ],
  "keywords": ["uni-app", "微信小程序", "云开发", "AI", "信息核查"],
  "author": "",
  "license": "MIT"
}

if (!fs.existsSync('package.json')) {
  fs.writeFileSync('package.json', JSON.stringify(rootPackageJson, null, 2))
  console.log('  ✅ 创建根目录 package.json')
} else {
  console.log('  ⚠️  根目录 package.json 已存在')
}

// backend package.json
const backendPackageJson = {
  "name": "news-verifier-backend",
  "version": "1.0.0",
  "description": "新闻核查助手后端服务",
  "scripts": {
    "deploy": "node scripts/deploy.js",
    "init": "node scripts/init.js",
    "test": "jest"
  },
  "dependencies": {
    "wx-server-sdk": "~2.6.3"
  },
  "devDependencies": {
    "jest": "^29.0.0"
  }
}

if (!fs.existsSync('backend/package.json')) {
  fs.writeFileSync('backend/package.json', JSON.stringify(backendPackageJson, null, 2))
  console.log('  ✅ 创建 backend/package.json')
} else {
  console.log('  ⚠️  backend/package.json 已存在')
}

// 6. 更新前端配置文件
console.log('\n🔧 更新前端配置...')
if (fs.existsSync('frontend/project.config.json')) {
  try {
    const projectConfig = JSON.parse(fs.readFileSync('frontend/project.config.json', 'utf8'))
    projectConfig.cloudfunctionRoot = '../backend/cloudfunctions/'
    projectConfig.miniprogramRoot = './'
    
    fs.writeFileSync('frontend/project.config.json', JSON.stringify(projectConfig, null, 2))
    console.log('  ✅ 更新 frontend/project.config.json')
  } catch (error) {
    console.log(`  ❌ 更新 project.config.json 失败: ${error.message}`)
  }
}

// 7. 创建 .gitignore
console.log('\n📝 创建 .gitignore...')
const gitignoreContent = `# 依赖
node_modules/
frontend/node_modules/
backend/node_modules/

# 构建输出
frontend/dist/
frontend/unpackage/

# 环境配置
.env
.env.local
.env.*.local

# IDE
.vscode/
.idea/

# 日志
*.log
npm-debug.log*

# 临时文件
.DS_Store
Thumbs.db

# 微信开发者工具
project.private.config.json
`

if (!fs.existsSync('.gitignore')) {
  fs.writeFileSync('.gitignore', gitignoreContent)
  console.log('  ✅ 创建 .gitignore')
} else {
  console.log('  ⚠️  .gitignore 已存在')
}

// 8. 创建 README.md
console.log('\n📖 创建项目 README...')
const readmeContent = `# AI赋能群聊信息核查助手

基于uni-app + 微信云开发的跨平台信息核查小程序。

## 项目结构

\`\`\`
news-verifier-ai/
├── frontend/          # 前端代码 (uni-app)
├── backend/           # 后端代码 (微信云开发)
├── docs/             # 项目文档
├── tools/            # 工具脚本
└── tests/            # 测试代码
\`\`\`

## 快速开始

### 安装依赖
\`\`\`bash
npm run install:all
\`\`\`

### 前端开发
\`\`\`bash
npm run dev:frontend
\`\`\`

### 后端部署
\`\`\`bash
npm run deploy:backend
\`\`\`

## 文档

- [快速开始](docs/deployment/快速开始.md)
- [部署指南](docs/deployment/微信云开发部署指南.md)
- [API文档](docs/api/API接口文档.md)
- [开发指南](docs/development/)

## 技术栈

- **前端**: uni-app + Vue 3 + TypeScript
- **后端**: 微信云开发 (云函数 + 云数据库 + 云存储)
- **AI服务**: OCR + LLM + 搜索引擎

## 许可证

MIT License
`

if (!fs.existsSync('README.md')) {
  fs.writeFileSync('README.md', readmeContent)
  console.log('  ✅ 创建 README.md')
} else {
  console.log('  ⚠️  README.md 已存在')
}

console.log('\n🎉 项目结构重构完成！')
console.log('\n📋 后续步骤:')
console.log('1. 检查文件移动是否正确')
console.log('2. 运行 npm run install:all 安装依赖')
console.log('3. 在微信开发者工具中打开 frontend 目录')
console.log('4. 配置云开发环境ID')
console.log('5. 部署云函数并测试功能')
console.log('\n📚 详细说明请查看: docs/deployment/快速开始.md')
